# Telegram群组监控工具

这是一个用于监控和记录Telegram群组信息的Python包，可以从指定网页抓取群组数据并存储到MongoDB数据库中。

## 功能特性

- 🕷️ 网页抓取：使用requests和BeautifulSoup抓取网页数据
- 📊 数据提取：提取群组名称、Telegram ID、订阅人数、排名等信息
- 🗄️ 数据存储：将数据保存到MongoDB数据库
- ⏰ 时间记录：记录数据抓取的时间戳
- 📝 日志记录：详细的日志记录和错误处理

## 安装依赖

```bash
pip install -r requirements.txt
```

## 数据库设置

确保MongoDB服务正在运行：
- 主机：localhost:27017
- 数据库：mydatabase
- 集合：tg_group

## 使用方法

### 方法1：使用命令行脚本

```bash
python run_monitor.py [URL]
```

或者运行后输入URL：
```bash
python run_monitor.py
```

### 方法2：使用Python代码

```python
from tg_group_monitor import monitor_telegram_groups

# 监控指定URL的群组信息
url = "https://example.com/telegram-groups"
success = monitor_telegram_groups(url)

if success:
    print("监控完成")
else:
    print("监控失败")
```

### 方法3：手动控制流程

```python
from tg_group_monitor import TelegramGroupScraper, MongoDBManager

# 创建抓取器
scraper = TelegramGroupScraper()

# 抓取数据
groups = scraper.scrape_groups(url)
print(f"抓取到 {len(groups)} 个群组")

# 保存到数据库
with MongoDBManager() as db:
    success_count = db.insert_groups(groups)
    print(f"成功保存 {success_count} 个群组")
```

## 数据结构

每个群组记录包含以下字段：

```json
{
    "name": "群组名称",
    "telegram_id": "群组ID（不含@符号）",
    "subscribers": 订阅人数,
    "rank": 排名,
    "recorded_time": "记录时间"
}
```

## 支持的网页格式

该工具专门设计用于解析包含以下结构的网页：

- 容器元素：`id="category-list-form"`的第二个div子元素
- 群组项目：`class="col-12 col-sm-6 col-md-4"`的div元素
- 群组名称：`class="font-16 text-dark text-truncate"`的div元素
- 订阅人数：`<b>`标签内的数字
- Telegram链接：包含`/channel/@`的href属性

## 测试

运行测试脚本验证HTML解析功能：

```bash
python test_parser.py
```

## 日志

程序会生成详细的日志文件：`tg_group_monitor.log`

## 注意事项

1. 确保MongoDB服务正在运行
2. 网络连接正常，能够访问目标网页
3. 目标网页的HTML结构符合预期格式
4. 遵守网站的robots.txt和使用条款

## 错误处理

- 网络连接失败：会重试并记录错误
- HTML解析失败：会跳过有问题的项目并继续处理
- 数据库连接失败：会记录错误并返回失败状态

## 许可证

MIT License
