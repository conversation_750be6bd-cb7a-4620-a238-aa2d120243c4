"""
数据对比模块
用于对比不同时间的监控结果
"""
import logging
from typing import List, Tuple, Optional
from datetime import datetime, timedelta
from .database import MongoDBManager
from .models import TelegramGroup


class DataComparator:
    """数据对比器"""
    
    def __init__(self, db_manager: MongoDBManager):
        """
        初始化数据对比器
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
    
    def get_latest_two_runs(self) -> Tuple[Optional[List[dict]], Optional[List[dict]]]:
        """
        获取最近两次运行的数据
        
        Returns:
            Tuple[Optional[List[dict]], Optional[List[dict]]]: (最新数据, 上次数据)
        """
        try:
            if self.db_manager.collection is None:
                self.logger.error("数据库连接未建立")
                return None, None
            
            # 获取所有不同的记录时间
            pipeline = [
                {
                    "$group": {
                        "_id": {
                            "$dateToString": {
                                "format": "%Y-%m-%d %H:%M:%S",
                                "date": "$recorded_time"
                            }
                        },
                        "recorded_time": {"$first": "$recorded_time"},
                        "count": {"$sum": 1}
                    }
                },
                {"$sort": {"recorded_time": -1}},
                {"$limit": 2}
            ]
            
            time_groups = list(self.db_manager.collection.aggregate(pipeline))
            
            if len(time_groups) < 2:
                self.logger.warning(f"数据库中只有 {len(time_groups)} 次运行记录，无法进行对比")
                if len(time_groups) == 1:
                    # 只有一次记录，返回最新的作为当前数据
                    latest_time = time_groups[0]["recorded_time"]
                    latest_data = self._get_data_by_time_range(latest_time, latest_time)
                    return latest_data, None
                return None, None
            
            # 获取最新和上次的时间
            latest_time = time_groups[0]["recorded_time"]
            previous_time = time_groups[1]["recorded_time"]
            
            self.logger.info(f"找到两次运行记录:")
            self.logger.info(f"  最新: {latest_time}")
            self.logger.info(f"  上次: {previous_time}")
            
            # 获取对应时间的数据
            latest_data = self._get_data_by_time_range(latest_time, latest_time)
            previous_data = self._get_data_by_time_range(previous_time, previous_time)
            
            return latest_data, previous_data
            
        except Exception as e:
            self.logger.error(f"获取最近两次运行数据失败: {e}")
            return None, None
    
    def _get_data_by_time_range(self, start_time: datetime, end_time: datetime) -> List[dict]:
        """
        根据时间范围获取数据
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            List[dict]: 群组数据列表
        """
        try:
            # 添加一些时间容差（1分钟）
            start_time_with_tolerance = start_time - timedelta(minutes=1)
            end_time_with_tolerance = end_time + timedelta(minutes=1)
            
            query = {
                "recorded_time": {
                    "$gte": start_time_with_tolerance,
                    "$lte": end_time_with_tolerance
                }
            }
            
            results = list(self.db_manager.collection.find(query).sort("rank", 1))
            self.logger.info(f"时间范围 {start_time} 到 {end_time} 找到 {len(results)} 条记录")
            
            return results
            
        except Exception as e:
            self.logger.error(f"根据时间范围获取数据失败: {e}")
            return []
    
    def find_new_groups(self, latest_data: List[dict], previous_data: List[dict]) -> List[TelegramGroup]:
        """
        找出新增的群组
        
        Args:
            latest_data: 最新数据
            previous_data: 上次数据
            
        Returns:
            List[TelegramGroup]: 新增群组列表
        """
        try:
            if not latest_data:
                self.logger.warning("最新数据为空")
                return []
            
            if not previous_data:
                self.logger.info("上次数据为空，所有群组都视为新增")
                return [self._dict_to_telegram_group(group) for group in latest_data]
            
            # 提取上次的telegram_id集合
            previous_ids = {group["telegram_id"] for group in previous_data}
            
            # 找出新增的群组
            new_groups = []
            for group_data in latest_data:
                if group_data["telegram_id"] not in previous_ids:
                    new_groups.append(self._dict_to_telegram_group(group_data))
            
            self.logger.info(f"找到 {len(new_groups)} 个新增群组")
            return new_groups
            
        except Exception as e:
            self.logger.error(f"查找新增群组失败: {e}")
            return []
    
    def _dict_to_telegram_group(self, group_data: dict) -> TelegramGroup:
        """
        将字典数据转换为TelegramGroup对象
        
        Args:
            group_data: 群组字典数据
            
        Returns:
            TelegramGroup: 群组对象
        """
        return TelegramGroup(
            name=group_data.get("name", ""),
            telegram_id=group_data.get("telegram_id", ""),
            subscribers=group_data.get("subscribers", 0),
            rank=group_data.get("rank", 0),
            recorded_time=group_data.get("recorded_time")
        )
    
    def get_latest_run_data(self) -> Optional[List[TelegramGroup]]:
        """
        获取最近一次运行的所有数据
        
        Returns:
            Optional[List[TelegramGroup]]: 最近一次的群组列表
        """
        try:
            if self.db_manager.collection is None:
                self.logger.error("数据库连接未建立")
                return None
            
            # 获取最新的记录时间
            latest_record = self.db_manager.collection.find().sort("recorded_time", -1).limit(1)
            latest_list = list(latest_record)
            
            if not latest_list:
                self.logger.warning("数据库中没有数据")
                return None
            
            latest_time = latest_list[0]["recorded_time"]
            self.logger.info(f"最新记录时间: {latest_time}")
            
            # 获取该时间的所有数据
            latest_data = self._get_data_by_time_range(latest_time, latest_time)
            
            if not latest_data:
                self.logger.warning("未找到最新数据")
                return None
            
            # 转换为TelegramGroup对象
            groups = [self._dict_to_telegram_group(group_data) for group_data in latest_data]
            
            self.logger.info(f"获取到最新运行的 {len(groups)} 个群组")
            return groups
            
        except Exception as e:
            self.logger.error(f"获取最新运行数据失败: {e}")
            return None
    
    def get_comparison_summary(self) -> dict:
        """
        获取对比摘要信息
        
        Returns:
            dict: 对比摘要
        """
        try:
            latest_data, previous_data = self.get_latest_two_runs()
            
            if not latest_data:
                return {
                    "status": "error",
                    "message": "无法获取最新数据"
                }
            
            new_groups = self.find_new_groups(latest_data, previous_data or [])
            
            latest_time = latest_data[0]["recorded_time"] if latest_data else None
            previous_time = previous_data[0]["recorded_time"] if previous_data else None
            
            return {
                "status": "success",
                "latest_time": latest_time,
                "previous_time": previous_time,
                "latest_count": len(latest_data),
                "previous_count": len(previous_data) if previous_data else 0,
                "new_groups_count": len(new_groups),
                "new_groups": new_groups
            }
            
        except Exception as e:
            self.logger.error(f"获取对比摘要失败: {e}")
            return {
                "status": "error",
                "message": str(e)
            }
