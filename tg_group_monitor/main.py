"""
主程序入口
"""
import logging
import sys
from typing import Optional
from .scraper import TelegramGroupScraper
from .database import MongoDBManager


def setup_logging(level: str = "INFO"):
    """
    设置日志配置
    
    Args:
        level: 日志级别
    """
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('tg_group_monitor.log', encoding='utf-8')
        ]
    )


def monitor_telegram_groups(url: str, 
                          mongo_host: str = "localhost", 
                          mongo_port: int = 27017,
                          database_name: str = "mydatabase") -> bool:
    """
    监控Telegram群组信息
    
    Args:
        url: 要抓取的网页URL
        mongo_host: MongoDB主机地址
        mongo_port: MongoDB端口
        database_name: 数据库名称
        
    Returns:
        bool: 执行是否成功
    """
    logger = logging.getLogger(__name__)
    
    try:
        # 初始化抓取器
        scraper = TelegramGroupScraper()
        logger.info("开始抓取Telegram群组信息...")
        
        # 抓取群组信息
        groups = scraper.scrape_groups(url)
        
        if not groups:
            logger.warning("没有抓取到任何群组信息")
            return False
        
        logger.info(f"成功抓取到 {len(groups)} 个群组信息")
        
        # 保存到数据库
        with MongoDBManager(mongo_host, mongo_port, database_name) as db_manager:
            success_count = db_manager.insert_groups(groups)
            
            if success_count == len(groups):
                logger.info(f"所有 {success_count} 个群组信息已成功保存到数据库")
                return True
            else:
                logger.warning(f"只有 {success_count}/{len(groups)} 个群组信息保存成功")
                return False
                
    except Exception as e:
        logger.error(f"监控过程中发生错误: {e}")
        return False


def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 示例URL - 请根据实际需要修改
    # 你需要提供具体的URL
    url = https://cn.tgstat.com/adult
    
    if not url:
        logger.error("URL不能为空")
        return
    
    logger.info(f"开始监控URL: {url}")
    
    success = monitor_telegram_groups(url)
    
    if success:
        logger.info("监控任务完成")
    else:
        logger.error("监控任务失败")


if __name__ == "__main__":
    main()
