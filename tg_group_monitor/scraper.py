"""
网页抓取模块
"""
import re
import logging
from typing import List, Optional
from datetime import datetime
import requests
from bs4 import BeautifulSoup
from tg_group_monitor.models import TelegramGroup


class TelegramGroupScraper:
    """Telegram群组信息抓取器"""
    
    def __init__(self, base_url: str = "https://cn.tgstat.com"):
        """
        初始化抓取器
        
        Args:
            base_url: 基础URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.logger = logging.getLogger(__name__)
        
        # 设置请求头，使用提供的headers
        self.session.headers.update({
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=0, i',
            'referer': 'https://cn.tgstat.com/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
        })

        # 设置cookies
        self.session.cookies.update({
            '_ym_uid': '1749649589135363175',
            '_ym_d': '1749649589',
            'tgstat_settings': 'ca2cfb56fe371c37ba7e98e9118a26d940c1de0bacc496a91ebc65c8ded82d4fa%3A2%3A%7Bi%3A0%3Bs%3A15%3A%22tgstat_settings%22%3Bi%3A1%3Bs%3A19%3A%22%7B%22fp%22%3A%22713XUUctVO%22%7D%22%3B%7D',
            '_tgstat_csrk': 'e011c69237c9c63293d6175552d31373b55c1d94ae110b48056b17783105e102a%3A2%3A%7Bi%3A0%3Bs%3A12%3A%22_tgstat_csrk%22%3Bi%3A1%3Bs%3A32%3A%22JtUD3xKGbHPCyKrlBV79_GOIku6obksZ%22%3B%7D',
            '_gid': 'GA1.2.1754007376.1752362987',
            '_ym_isad': '1',
            '_ga': 'GA1.2.1589117274.1749649584',
            '_ga_ZEKJ7V8PH3': 'GS2.1.s1752386629$o3$g1$t1752387161$j35$l0$h0'
        })
    
    def fetch_page(self, url: str) -> Optional[str]:
        """
        获取网页内容
        
        Args:
            url: 目标URL
            
        Returns:
            Optional[str]: 网页HTML内容，失败时返回None
        """
        try:
            self.logger.info(f"正在获取网页: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            self.logger.info(f"成功获取网页内容，长度: {len(response.text)}")
            return response.text
        except requests.RequestException as e:
            self.logger.error(f"获取网页失败: {e}")
            return None
    
    def parse_subscriber_count(self, text: str) -> int:
        """
        解析订阅人数
        
        Args:
            text: 包含订阅人数的文本
            
        Returns:
            int: 订阅人数
        """
        try:
            # 移除所有空格和非数字字符，但保留数字
            # 例如: "559 838" -> "559838"
            numbers = re.findall(r'\d+', text.replace(' ', ''))
            if numbers:
                # 如果有多个数字，连接它们
                subscriber_str = ''.join(numbers)
                return int(subscriber_str)
            return 0
        except (ValueError, AttributeError):
            self.logger.warning(f"无法解析订阅人数: {text}")
            return 0
    
    def extract_telegram_id(self, href: str) -> str:
        """
        从链接中提取Telegram ID
        
        Args:
            href: 链接地址
            
        Returns:
            str: Telegram ID (不包含@符号)
        """
        try:
            # 从URL中提取@后面的部分
            # 例如: "https://cn.tgstat.com/channel/@dyf18" -> "dyf18"
            match = re.search(r'/@([^/]+)', href)
            if match:
                return match.group(1)
            return ""
        except AttributeError:
            self.logger.warning(f"无法提取Telegram ID: {href}")
            return ""
    
    def parse_groups_from_html(self, html: str) -> List[TelegramGroup]:
        """
        从HTML中解析群组信息
        
        Args:
            html: 网页HTML内容
            
        Returns:
            List[TelegramGroup]: 群组信息列表
        """
        groups = []
        
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # 根据提供的xpath查找容器
            # xpath: //*[@id="category-list-form"]/div[2]
            container = soup.find(attrs={'id': 'category-list-form'})
            if not container:
                self.logger.error("未找到category-list-form容器")
                return groups
            
            # 查找第二个div子元素
            div_children = container.find_all('div', recursive=False)
            if len(div_children) < 2:
                self.logger.error("category-list-form容器中没有找到第二个div")
                return groups
            
            list_container = div_children[1]
            
            # 查找所有群组项目
            group_items = list_container.find_all('div', class_='col-12 col-sm-6 col-md-4')
            
            self.logger.info(f"找到 {len(group_items)} 个群组项目")
            
            for rank, item in enumerate(group_items, 1):
                try:
                    group = self._parse_single_group(item, rank)
                    if group:
                        groups.append(group)
                        self.logger.debug(f"解析群组成功: {group}")
                except Exception as e:
                    self.logger.error(f"解析第 {rank} 个群组时出错: {e}")
                    continue
            
            self.logger.info(f"成功解析 {len(groups)} 个群组信息")
            
        except Exception as e:
            self.logger.error(f"解析HTML时出错: {e}")
        
        return groups
    
    def _parse_single_group(self, item_element, rank: int) -> Optional[TelegramGroup]:
        """
        解析单个群组项目
        
        Args:
            item_element: BeautifulSoup元素
            rank: 排名
            
        Returns:
            Optional[TelegramGroup]: 群组信息，解析失败时返回None
        """
        try:
            # 查找包含channel链接的元素（可能有多个a标签）
            link_elements = item_element.find_all('a', href=True)
            channel_link = None

            for link in link_elements:
                href = link.get('href', '')
                if '/channel/@' in href:
                    channel_link = link
                    break

            if not channel_link:
                self.logger.warning("未找到channel链接元素")
                return None

            href = channel_link.get('href', '')
            telegram_id = self.extract_telegram_id(href)
            
            if not telegram_id:
                self.logger.warning(f"无法提取Telegram ID: {href}")
                return None
            
            # 查找群组名称
            name_element = item_element.find('div', class_='font-16 text-dark text-truncate')
            if not name_element:
                self.logger.warning("未找到群组名称元素")
                return None
            
            name = name_element.get_text(strip=True)
            
            # 查找订阅人数
            subscriber_element = item_element.find('b')
            if not subscriber_element:
                self.logger.warning("未找到订阅人数元素")
                return None
            
            subscriber_text = subscriber_element.get_text(strip=True)
            subscribers = self.parse_subscriber_count(subscriber_text)
            
            # 创建群组对象
            group = TelegramGroup(
                name=name,
                telegram_id=telegram_id,
                subscribers=subscribers,
                rank=rank,
                recorded_time=datetime.now()
            )
            
            return group
            
        except Exception as e:
            self.logger.error(f"解析单个群组时出错: {e}")
            return None
    
    def scrape_groups(self, url: str) -> List[TelegramGroup]:
        """
        抓取指定URL的群组信息
        
        Args:
            url: 目标URL
            
        Returns:
            List[TelegramGroup]: 群组信息列表
        """
        html = self.fetch_page(url)
        if not html:
            return []
        
        return self.parse_groups_from_html(html)
