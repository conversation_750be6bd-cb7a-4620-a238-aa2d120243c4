"""
网页抓取模块
"""
import re
import logging
from typing import List, Optional
from datetime import datetime
import requests
from bs4 import BeautifulSoup
from .models import TelegramGroup


class TelegramGroupScraper:
    """Telegram群组信息抓取器"""
    
    def __init__(self, base_url: str = "https://cn.tgstat.com"):
        """
        初始化抓取器
        
        Args:
            base_url: 基础URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.logger = logging.getLogger(__name__)
        
        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def fetch_page(self, url: str) -> Optional[str]:
        """
        获取网页内容
        
        Args:
            url: 目标URL
            
        Returns:
            Optional[str]: 网页HTML内容，失败时返回None
        """
        try:
            self.logger.info(f"正在获取网页: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            self.logger.info(f"成功获取网页内容，长度: {len(response.text)}")
            return response.text
        except requests.RequestException as e:
            self.logger.error(f"获取网页失败: {e}")
            return None
    
    def parse_subscriber_count(self, text: str) -> int:
        """
        解析订阅人数
        
        Args:
            text: 包含订阅人数的文本
            
        Returns:
            int: 订阅人数
        """
        try:
            # 移除所有空格和非数字字符，但保留数字
            # 例如: "559 838" -> "559838"
            numbers = re.findall(r'\d+', text.replace(' ', ''))
            if numbers:
                # 如果有多个数字，连接它们
                subscriber_str = ''.join(numbers)
                return int(subscriber_str)
            return 0
        except (ValueError, AttributeError):
            self.logger.warning(f"无法解析订阅人数: {text}")
            return 0
    
    def extract_telegram_id(self, href: str) -> str:
        """
        从链接中提取Telegram ID
        
        Args:
            href: 链接地址
            
        Returns:
            str: Telegram ID (不包含@符号)
        """
        try:
            # 从URL中提取@后面的部分
            # 例如: "https://cn.tgstat.com/channel/@dyf18" -> "dyf18"
            match = re.search(r'/@([^/]+)', href)
            if match:
                return match.group(1)
            return ""
        except AttributeError:
            self.logger.warning(f"无法提取Telegram ID: {href}")
            return ""
    
    def parse_groups_from_html(self, html: str) -> List[TelegramGroup]:
        """
        从HTML中解析群组信息
        
        Args:
            html: 网页HTML内容
            
        Returns:
            List[TelegramGroup]: 群组信息列表
        """
        groups = []
        
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # 根据提供的xpath查找容器
            # xpath: //*[@id="category-list-form"]/div[2]
            container = soup.find(attrs={'id': 'category-list-form'})
            if not container:
                self.logger.error("未找到category-list-form容器")
                return groups
            
            # 查找第二个div子元素
            div_children = container.find_all('div', recursive=False)
            if len(div_children) < 2:
                self.logger.error("category-list-form容器中没有找到第二个div")
                return groups
            
            list_container = div_children[1]
            
            # 查找所有群组项目
            group_items = list_container.find_all('div', class_='col-12 col-sm-6 col-md-4')
            
            self.logger.info(f"找到 {len(group_items)} 个群组项目")
            
            for rank, item in enumerate(group_items, 1):
                try:
                    group = self._parse_single_group(item, rank)
                    if group:
                        groups.append(group)
                        self.logger.debug(f"解析群组成功: {group}")
                except Exception as e:
                    self.logger.error(f"解析第 {rank} 个群组时出错: {e}")
                    continue
            
            self.logger.info(f"成功解析 {len(groups)} 个群组信息")
            
        except Exception as e:
            self.logger.error(f"解析HTML时出错: {e}")
        
        return groups
    
    def _parse_single_group(self, item_element, rank: int) -> Optional[TelegramGroup]:
        """
        解析单个群组项目
        
        Args:
            item_element: BeautifulSoup元素
            rank: 排名
            
        Returns:
            Optional[TelegramGroup]: 群组信息，解析失败时返回None
        """
        try:
            # 查找包含channel链接的元素（可能有多个a标签）
            link_elements = item_element.find_all('a', href=True)
            channel_link = None

            for link in link_elements:
                href = link.get('href', '')
                if '/channel/@' in href:
                    channel_link = link
                    break

            if not channel_link:
                self.logger.warning("未找到channel链接元素")
                return None

            href = channel_link.get('href', '')
            telegram_id = self.extract_telegram_id(href)
            
            if not telegram_id:
                self.logger.warning(f"无法提取Telegram ID: {href}")
                return None
            
            # 查找群组名称
            name_element = item_element.find('div', class_='font-16 text-dark text-truncate')
            if not name_element:
                self.logger.warning("未找到群组名称元素")
                return None
            
            name = name_element.get_text(strip=True)
            
            # 查找订阅人数
            subscriber_element = item_element.find('b')
            if not subscriber_element:
                self.logger.warning("未找到订阅人数元素")
                return None
            
            subscriber_text = subscriber_element.get_text(strip=True)
            subscribers = self.parse_subscriber_count(subscriber_text)
            
            # 创建群组对象
            group = TelegramGroup(
                name=name,
                telegram_id=telegram_id,
                subscribers=subscribers,
                rank=rank,
                recorded_time=datetime.now()
            )
            
            return group
            
        except Exception as e:
            self.logger.error(f"解析单个群组时出错: {e}")
            return None
    
    def scrape_groups(self, url: str) -> List[TelegramGroup]:
        """
        抓取指定URL的群组信息
        
        Args:
            url: 目标URL
            
        Returns:
            List[TelegramGroup]: 群组信息列表
        """
        html = self.fetch_page(url)
        if not html:
            return []
        
        return self.parse_groups_from_html(html)
