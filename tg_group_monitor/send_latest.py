#!/usr/bin/env python3
"""
发送最近一次的所有群组到Telegram
"""

import logging
import sys
from datetime import datetime
from .database import MongoDBManager
from .comparator import DataComparator
from .telegram_bot import TelegramBot

# Telegram Bot配置
BOT_TOKEN = '**********************************************'
CHAT_ID = '-1002898774251'

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('tg_latest.log', encoding='utf-8')
        ]
    )

def send_latest_groups():
    """发送最近一次的所有群组到Telegram"""
    logger = logging.getLogger(__name__)
    
    try:
        print("=== 发送最新群组列表到Telegram ===")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 初始化组件
        print("🔄 初始化数据库连接...")
        with MongoDBManager() as db_manager:
            comparator = DataComparator(db_manager)
            bot = TelegramBot(BOT_TOKEN, CHAT_ID)
            
            # 测试Bot连接
            print("🤖 测试Telegram Bot连接...")
            if not bot.test_connection():
                print("❌ Telegram Bot连接失败")
                return False
            
            print("✅ Telegram Bot连接成功")
            
            # 获取最新数据
            print("📊 获取最新群组数据...")
            latest_groups = comparator.get_latest_run_data()
            
            if not latest_groups:
                print("❌ 无法获取最新群组数据")
                return False
            
            # 显示数据信息
            print(f"\n📈 数据信息:")
            print(f"   记录时间: {latest_groups[0].recorded_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   群组数量: {len(latest_groups)}")
            
            # 显示前几个群组
            print(f"\n🏆 前5名群组:")
            for i, group in enumerate(latest_groups[:5], 1):
                print(f"   {i}. {group.name}：@{group.telegram_id} ({group.subscribers:,} 订阅者)")
            
            # 格式化消息标题
            record_time = latest_groups[0].recorded_time.strftime('%Y-%m-%d %H:%M:%S')
            title = f"📊 最新Telegram群组排行榜\n🕐 记录时间: {record_time}"
            
            # 格式化消息
            print(f"\n📤 准备发送Telegram消息...")
            message = bot.format_groups_message(latest_groups, title)
            
            # 显示消息长度信息
            print(f"📝 消息长度: {len(message)} 字符")
            if len(message) > 4000:
                print("⚠️ 消息较长，将分多条发送")
            
            # 发送消息
            print(f"🚀 发送消息到Telegram群组...")
            success = bot.send_message(message)
            
            if success:
                print("✅ 群组列表发送成功")
                logger.info(f"群组列表发送成功: {len(latest_groups)} 个群组")
                return True
            else:
                print("❌ 群组列表发送失败")
                return False
                
    except Exception as e:
        print(f"❌ 发送群组列表时出错: {e}")
        logger.error(f"发送群组列表失败: {e}")
        return False

def main():
    """主函数"""
    setup_logging()
    
    try:
        success = send_latest_groups()
        
        if success:
            print(f"\n🎉 群组列表发送任务完成")
        else:
            print(f"\n💥 群组列表发送任务失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
