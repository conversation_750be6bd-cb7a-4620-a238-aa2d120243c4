#!/usr/bin/env python3
"""
最终使用示例 - 完整的Telegram群组监控流程
"""

import logging
import sys
from datetime import datetime
from .scraper import TelegramGroupScraper
from .database import MongoDBManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("=== Telegram群组监控最终示例 ===")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 目标URL
    url = "https://cn.tgstat.com/adult"
    
    print(f"🎯 目标URL: {url}")
    print("🚀 开始抓取...")
    
    try:
        # 步骤1: 创建抓取器并抓取数据
        scraper = TelegramGroupScraper()
        groups = scraper.scrape_groups(url)
        
        if not groups:
            print("❌ 没有抓取到群组数据")
            return
        
        print(f"✅ 成功抓取到 {len(groups)} 个群组")
        
        # 步骤2: 显示抓取结果摘要
        print(f"\n📊 数据摘要:")
        total_subscribers = sum(g.subscribers for g in groups)
        avg_subscribers = total_subscribers / len(groups)
        print(f"   总群组数: {len(groups)}")
        print(f"   总订阅人数: {total_subscribers:,}")
        print(f"   平均订阅人数: {avg_subscribers:,.0f}")
        
        # 显示前5名
        print(f"\n🏆 前5名群组:")
        for i, group in enumerate(groups[:5], 1):
            print(f"   {i}. {group.name[:35]:<35} @{group.telegram_id:<12} {group.subscribers:>7,} 订阅者")
        
        # 步骤3: 询问是否保存到数据库
        save_to_db = input(f"\n💾 是否将 {len(groups)} 个群组保存到MongoDB? (y/N): ").strip().lower()
        
        if save_to_db in ['y', 'yes', '是']:
            print("🔄 正在保存到数据库...")
            
            try:
                with MongoDBManager() as db_manager:
                    success_count = db_manager.insert_groups(groups)
                    
                    if success_count == len(groups):
                        print(f"✅ 成功保存所有 {success_count} 个群组到数据库")
                        
                        # 显示数据库信息
                        print(f"\n🗄️ 数据库信息:")
                        print(f"   主机: localhost:27017")
                        print(f"   数据库: mydatabase")
                        print(f"   集合: tg_group")
                        print(f"   记录时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                        
                        # 查询验证
                        today = datetime.now().strftime('%Y-%m-%d')
                        today_groups = db_manager.get_groups_by_date(today)
                        print(f"   今日总记录: {len(today_groups)} 个")
                        
                    else:
                        print(f"⚠️ 只保存了 {success_count}/{len(groups)} 个群组")
                        
            except Exception as e:
                print(f"❌ 保存到数据库失败: {e}")
                logger.error(f"数据库保存失败: {e}")
        else:
            print("⏭️ 跳过数据库保存")
        
        # 步骤4: 生成报告
        print(f"\n📋 监控报告:")
        print(f"   抓取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   数据源: {url}")
        print(f"   群组数量: {len(groups)}")
        print(f"   数据质量: {'✅ 良好' if len(groups) > 90 else '⚠️ 需要检查'}")
        
        # 保存简单的CSV报告
        csv_filename = f"tg_groups_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        with open(csv_filename, 'w', encoding='utf-8') as f:
            f.write("排名,群组名称,Telegram ID,订阅人数,记录时间\n")
            for group in groups:
                f.write(f"{group.rank},{group.name},{group.telegram_id},{group.subscribers},{group.recorded_time}\n")
        
        print(f"   CSV报告: {csv_filename}")

        # 询问是否发送Telegram对比报告
        send_tg = input(f"\n📤 是否发送对比报告到Telegram? (y/N): ").strip().lower()

        if send_tg in ['y', 'yes', '是']:
            print("🚀 正在发送Telegram对比报告...")
            try:
                from .send_comparison import send_comparison_report
                if send_comparison_report():
                    print("✅ Telegram对比报告发送成功")
                else:
                    print("❌ Telegram对比报告发送失败")
            except Exception as e:
                print(f"❌ 发送Telegram报告时出错: {e}")
        else:
            print("⏭️ 跳过Telegram报告发送")

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        logger.error(f"监控过程出错: {e}")

    print(f"\n🏁 监控完成")

if __name__ == "__main__":
    main()
