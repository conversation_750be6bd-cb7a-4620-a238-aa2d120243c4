#!/usr/bin/env python3
"""
查询MongoDB中的Telegram群组数据
"""

import sys
from datetime import datetime
from .database import MongoDBManager

def format_group_info(group_data):
    """格式化群组信息显示"""
    return f"""
📊 群组信息:
   名称: {group_data.get('name', 'N/A')}
   ID: @{group_data.get('telegram_id', 'N/A')}
   订阅人数: {group_data.get('subscribers', 0):,}
   排名: #{group_data.get('rank', 'N/A')}
   记录时间: {group_data.get('recorded_time', 'N/A')}
"""

def query_by_date(date_str):
    """按日期查询数据"""
    print(f"🔍 查询日期: {date_str}")
    
    with MongoDBManager() as db:
        groups = db.get_groups_by_date(date_str)
        
        if not groups:
            print("❌ 没有找到数据")
            return
        
        print(f"✅ 找到 {len(groups)} 个群组记录")
        print("=" * 50)
        
        for i, group in enumerate(groups, 1):
            print(f"\n[{i}] {format_group_info(group)}")

def query_all_recent():
    """查询最近的所有数据"""
    print("🔍 查询所有最近数据...")
    
    try:
        with MongoDBManager() as db:
            if db.collection is None:
                print("❌ 数据库连接失败")
                return
            
            # 查询最近100条记录
            cursor = db.collection.find().sort("recorded_time", -1).limit(100)
            groups = list(cursor)
            
            if not groups:
                print("❌ 数据库中没有数据")
                return
            
            print(f"✅ 找到 {len(groups)} 个群组记录")
            print("=" * 50)
            
            # 按日期分组显示
            dates = {}
            for group in groups:
                date_key = group['recorded_time'].strftime('%Y-%m-%d')
                if date_key not in dates:
                    dates[date_key] = []
                dates[date_key].append(group)
            
            for date_key in sorted(dates.keys(), reverse=True):
                print(f"\n📅 {date_key} ({len(dates[date_key])} 个群组)")
                print("-" * 30)
                
                for i, group in enumerate(dates[date_key], 1):
                    print(f"[{i}] {group['name']} (@{group['telegram_id']}) - {group['subscribers']:,} 订阅者 (排名#{group['rank']})")
                    
    except Exception as e:
        print(f"❌ 查询失败: {e}")

def show_statistics():
    """显示统计信息"""
    print("📈 数据库统计信息")
    
    try:
        with MongoDBManager() as db:
            if db.collection is None:
                print("❌ 数据库连接失败")
                return
            
            # 总记录数
            total_count = db.collection.count_documents({})
            print(f"📊 总记录数: {total_count}")
            
            if total_count == 0:
                print("❌ 数据库中没有数据")
                return
            
            # 最新记录时间
            latest = db.collection.find().sort("recorded_time", -1).limit(1)
            latest_record = list(latest)[0]
            print(f"🕐 最新记录时间: {latest_record['recorded_time']}")
            
            # 最早记录时间
            earliest = db.collection.find().sort("recorded_time", 1).limit(1)
            earliest_record = list(earliest)[0]
            print(f"🕐 最早记录时间: {earliest_record['recorded_time']}")
            
            # 不同日期的记录数
            pipeline = [
                {
                    "$group": {
                        "_id": {
                            "$dateToString": {
                                "format": "%Y-%m-%d",
                                "date": "$recorded_time"
                            }
                        },
                        "count": {"$sum": 1}
                    }
                },
                {"$sort": {"_id": -1}}
            ]
            
            date_counts = list(db.collection.aggregate(pipeline))
            print(f"\n📅 按日期统计:")
            for item in date_counts:
                print(f"   {item['_id']}: {item['count']} 条记录")
                
    except Exception as e:
        print(f"❌ 统计失败: {e}")

def main():
    """主函数"""
    print("=== Telegram群组数据查询工具 ===\n")
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "stats":
            show_statistics()
        elif command == "all":
            query_all_recent()
        elif len(command) == 10 and command.count('-') == 2:  # 日期格式 YYYY-MM-DD
            query_by_date(command)
        else:
            print("❌ 无效的命令或日期格式")
            print("用法:")
            print("  python query_data.py stats          # 显示统计信息")
            print("  python query_data.py all            # 显示所有最近数据")
            print("  python query_data.py 2024-01-01     # 按日期查询")
    else:
        print("请选择操作:")
        print("1. 显示统计信息")
        print("2. 显示所有最近数据")
        print("3. 按日期查询")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            show_statistics()
        elif choice == "2":
            query_all_recent()
        elif choice == "3":
            date_str = input("请输入日期 (YYYY-MM-DD): ").strip()
            if date_str:
                query_by_date(date_str)
            else:
                print("❌ 日期不能为空")
        else:
            print("❌ 无效的选择")

if __name__ == "__main__":
    main()
