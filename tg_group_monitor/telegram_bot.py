"""
Telegram Bot模块
用于发送监控结果到Telegram群组
"""
import logging
import requests
from typing import List, Optional
from datetime import datetime
from .models import TelegramGroup


class TelegramBot:
    """Telegram Bot管理器"""
    
    def __init__(self, bot_token: str, chat_id: str):
        """
        初始化Telegram Bot
        
        Args:
            bot_token: Bot令牌
            chat_id: 目标群组ID
        """
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.base_url = f"https://api.telegram.org/bot{bot_token}"
        self.logger = logging.getLogger(__name__)
    
    def send_message(self, text: str, parse_mode: str = "HTML") -> bool:
        """
        发送消息到Telegram群组
        
        Args:
            text: 消息内容
            parse_mode: 解析模式 (HTML/Markdown)
            
        Returns:
            bool: 发送是否成功
        """
        try:
            url = f"{self.base_url}/sendMessage"
            
            # 分割长消息（Telegram限制4096字符）
            max_length = 4000  # 留一些余量
            
            if len(text) <= max_length:
                return self._send_single_message(text, parse_mode)
            else:
                return self._send_long_message(text, parse_mode, max_length)
                
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
    
    def _send_single_message(self, text: str, parse_mode: str) -> bool:
        """发送单条消息"""
        try:
            url = f"{self.base_url}/sendMessage"
            data = {
                "chat_id": self.chat_id,
                "text": text,
                "parse_mode": parse_mode
            }
            
            response = requests.post(url, data=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get("ok"):
                self.logger.info("消息发送成功")
                return True
            else:
                error_desc = result.get('description', 'Unknown error')
                error_code = result.get('error_code', 'Unknown')
                self.logger.error(f"消息发送失败 (错误码: {error_code}): {error_desc}")
                return False
                
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP错误: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_detail = e.response.json()
                    self.logger.error(f"详细错误信息: {error_detail}")
                except:
                    self.logger.error(f"响应内容: {e.response.text}")
            return False
        except Exception as e:
            self.logger.error(f"发送单条消息失败: {e}")
            return False
    
    def _send_long_message(self, text: str, parse_mode: str, max_length: int) -> bool:
        """分割并发送长消息"""
        try:
            lines = text.split('\n')
            current_message = ""
            success_count = 0
            total_parts = 0
            
            for line in lines:
                # 检查添加这一行是否会超过限制
                if len(current_message + line + '\n') > max_length:
                    if current_message:
                        # 发送当前消息
                        if self._send_single_message(current_message.strip(), parse_mode):
                            success_count += 1
                        total_parts += 1
                        current_message = ""
                    
                    # 如果单行就超过限制，需要进一步分割
                    if len(line) > max_length:
                        parts = [line[i:i+max_length] for i in range(0, len(line), max_length)]
                        for part in parts:
                            if self._send_single_message(part, parse_mode):
                                success_count += 1
                            total_parts += 1
                    else:
                        current_message = line + '\n'
                else:
                    current_message += line + '\n'
            
            # 发送最后一部分
            if current_message.strip():
                if self._send_single_message(current_message.strip(), parse_mode):
                    success_count += 1
                total_parts += 1
            
            self.logger.info(f"长消息发送完成: {success_count}/{total_parts} 部分成功")
            return success_count == total_parts
            
        except Exception as e:
            self.logger.error(f"发送长消息失败: {e}")
            return False
    
    def format_groups_message(self, groups: List[TelegramGroup], title: str) -> str:
        """
        格式化群组列表消息
        
        Args:
            groups: 群组列表
            title: 消息标题
            
        Returns:
            str: 格式化的消息
        """
        if not groups:
            return f"<b>{title}</b>\n\n暂无群组数据"
        
        message_lines = [f"<b>{title}</b>"]
        message_lines.append(f"📊 总计: {len(groups)} 个群组")
        message_lines.append("")
        
        for i, group in enumerate(groups, 1):
            # 格式: 群组名：@群组id
            line = f"{i}. {group.name}：@{group.telegram_id}"
            message_lines.append(line)
        
        return "\n".join(message_lines)
    
    def format_comparison_message(self, 
                                previous_time: datetime, 
                                current_time: datetime,
                                new_groups: List[TelegramGroup],
                                total_current: int,
                                total_previous: int) -> str:
        """
        格式化对比消息
        
        Args:
            previous_time: 上次运行时间
            current_time: 本次运行时间
            new_groups: 新增群组列表
            total_current: 本次总群组数
            total_previous: 上次总群组数
            
        Returns:
            str: 格式化的对比消息
        """
        message_lines = ["<b>🔄 Telegram群组监控对比报告</b>"]
        message_lines.append("")
        
        # 时间信息
        message_lines.append(f"⏰ <b>时间对比:</b>")
        message_lines.append(f"上次运行: {previous_time.strftime('%Y-%m-%d %H:%M:%S')}")
        message_lines.append(f"本次运行: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        message_lines.append("")
        
        # 数量对比
        message_lines.append(f"📊 <b>数量对比:</b>")
        message_lines.append(f"上次群组数: {total_previous}")
        message_lines.append(f"本次群组数: {total_current}")
        message_lines.append(f"新增群组数: {len(new_groups)}")
        message_lines.append("")
        
        # 新增群组列表
        if new_groups:
            message_lines.append(f"🆕 <b>新增群组列表:</b>")
            for i, group in enumerate(new_groups, 1):
                line = f"{i}. {group.name}：@{group.telegram_id}"
                message_lines.append(line)
        else:
            message_lines.append("✅ <b>无新增群组</b>")
        
        return "\n".join(message_lines)
    
    def test_connection(self) -> bool:
        """
        测试Bot连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            url = f"{self.base_url}/getMe"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if result.get("ok"):
                bot_info = result.get("result", {})
                self.logger.info(f"Bot连接成功: {bot_info.get('username', 'Unknown')}")
                return True
            else:
                self.logger.error(f"Bot连接失败: {result.get('description')}")
                return False
                
        except Exception as e:
            self.logger.error(f"测试Bot连接失败: {e}")
            return False
