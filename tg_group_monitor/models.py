"""
数据模型定义
"""
from datetime import datetime
from typing import Optional


class TelegramGroup:
    """Telegram群组信息模型"""
    
    def __init__(self, name: str, telegram_id: str, subscribers: int, rank: int, recorded_time: Optional[datetime] = None):
        self.name = name
        self.telegram_id = telegram_id
        self.subscribers = subscribers
        self.rank = rank
        self.recorded_time = recorded_time or datetime.now()
    
    def to_dict(self) -> dict:
        """转换为字典格式，用于MongoDB存储"""
        return {
            'name': self.name,
            'telegram_id': self.telegram_id,
            'subscribers': self.subscribers,
            'rank': self.rank,
            'recorded_time': self.recorded_time
        }
    
    def __str__(self) -> str:
        return f"TelegramGroup(name='{self.name}', id='{self.telegram_id}', subscribers={self.subscribers}, rank={self.rank})"
    
    def __repr__(self) -> str:
        return self.__str__()
