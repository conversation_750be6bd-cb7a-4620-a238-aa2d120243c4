#!/usr/bin/env python3
"""
对比数据库结果并发送新增群组到Telegram
"""

import logging
import sys
from datetime import datetime
from .database import MongoDBManager
from .comparator import DataComparator
from .telegram_bot import TelegramBot

# Telegram Bot配置
BOT_TOKEN = '**********************************************'
CHAT_ID = '-1002898774251'

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('tg_comparison.log', encoding='utf-8')
        ]
    )

def send_comparison_report():
    """发送对比报告到Telegram"""
    logger = logging.getLogger(__name__)
    
    try:
        print("=== Telegram群组监控对比报告 ===")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 初始化组件
        print("🔄 初始化数据库连接...")
        with MongoDBManager() as db_manager:
            comparator = DataComparator(db_manager)
            bot = TelegramBot(BOT_TOKEN, CHAT_ID)
            
            # 测试Bot连接
            print("🤖 测试Telegram Bot连接...")
            if not bot.test_connection():
                print("❌ Telegram Bot连接失败")
                return False
            
            print("✅ Telegram Bot连接成功")
            
            # 获取对比数据
            print("📊 获取数据库对比数据...")
            latest_data, previous_data = comparator.get_latest_two_runs()
            
            if not latest_data:
                print("❌ 无法获取最新数据")
                return False
            
            # 计算新增群组
            new_groups = comparator.find_new_groups(latest_data, previous_data or [])
            
            # 获取时间信息
            latest_time = latest_data[0]["recorded_time"] if latest_data else datetime.now()
            previous_time = previous_data[0]["recorded_time"] if previous_data else None
            
            # 显示对比结果
            print(f"\n📈 对比结果:")
            print(f"   最新运行时间: {latest_time.strftime('%Y-%m-%d %H:%M:%S')}")
            if previous_time:
                print(f"   上次运行时间: {previous_time.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"   上次群组数量: {len(previous_data)}")
            else:
                print(f"   上次运行时间: 无")
                print(f"   上次群组数量: 0")
            
            print(f"   本次群组数量: {len(latest_data)}")
            print(f"   新增群组数量: {len(new_groups)}")
            
            if new_groups:
                print(f"\n🆕 新增群组:")
                for i, group in enumerate(new_groups[:5], 1):  # 只显示前5个
                    print(f"   {i}. {group.name}：@{group.telegram_id}")
                if len(new_groups) > 5:
                    print(f"   ... 还有 {len(new_groups) - 5} 个群组")
            
            # 格式化消息
            print(f"\n📤 准备发送Telegram消息...")
            message = bot.format_comparison_message(
                previous_time or datetime.now(),
                latest_time,
                new_groups,
                len(latest_data),
                len(previous_data) if previous_data else 0
            )
            
            # 发送消息
            print(f"🚀 发送消息到Telegram群组...")
            success = bot.send_message(message)
            
            if success:
                print("✅ 对比报告发送成功")
                logger.info(f"对比报告发送成功: 新增 {len(new_groups)} 个群组")
                return True
            else:
                print("❌ 对比报告发送失败")
                return False
                
    except Exception as e:
        print(f"❌ 发送对比报告时出错: {e}")
        logger.error(f"发送对比报告失败: {e}")
        return False

def main():
    """主函数"""
    setup_logging()
    
    try:
        success = send_comparison_report()
        
        if success:
            print(f"\n🎉 对比报告任务完成")
        else:
            print(f"\n💥 对比报告任务失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
