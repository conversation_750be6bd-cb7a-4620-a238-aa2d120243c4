"""
MongoDB数据库操作模块
"""
import logging
from typing import List, Optional
from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.database import Database
from tg_group_monitor.models import TelegramGroup


class MongoDBManager:
    """MongoDB数据库管理器"""
    
    def __init__(self, host: str = "localhost", port: int = 27017, database_name: str = "mydatabase"):
        """
        初始化MongoDB连接
        
        Args:
            host: MongoDB主机地址
            port: MongoDB端口
            database_name: 数据库名称
        """
        self.host = host
        self.port = port
        self.database_name = database_name
        self.client: Optional[MongoClient] = None
        self.database: Optional[Database] = None
        self.collection: Optional[Collection] = None
        
        self.logger = logging.getLogger(__name__)
    
    def connect(self) -> bool:
        """
        连接到MongoDB
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.client = MongoClient(self.host, self.port)
            # 测试连接
            self.client.admin.command('ping')
            self.database = self.client[self.database_name]
            self.collection = self.database['tg_group']
            self.logger.info(f"成功连接到MongoDB: {self.host}:{self.port}/{self.database_name}")
            return True
        except Exception as e:
            self.logger.error(f"连接MongoDB失败: {e}")
            return False
    
    def disconnect(self):
        """断开MongoDB连接"""
        if self.client:
            self.client.close()
            self.logger.info("已断开MongoDB连接")
    
    def insert_group(self, group: TelegramGroup) -> bool:
        """
        插入单个群组信息
        
        Args:
            group: TelegramGroup对象
            
        Returns:
            bool: 插入是否成功
        """
        try:
            if not self.collection:
                self.logger.error("数据库连接未建立")
                return False
            
            result = self.collection.insert_one(group.to_dict())
            self.logger.info(f"成功插入群组信息: {group.name} (ID: {result.inserted_id})")
            return True
        except Exception as e:
            self.logger.error(f"插入群组信息失败: {e}")
            return False
    
    def insert_groups(self, groups: List[TelegramGroup]) -> int:
        """
        批量插入群组信息
        
        Args:
            groups: TelegramGroup对象列表
            
        Returns:
            int: 成功插入的数量
        """
        try:
            if not self.collection:
                self.logger.error("数据库连接未建立")
                return 0
            
            if not groups:
                self.logger.warning("没有群组信息需要插入")
                return 0
            
            documents = [group.to_dict() for group in groups]
            result = self.collection.insert_many(documents)
            success_count = len(result.inserted_ids)
            self.logger.info(f"成功批量插入 {success_count} 个群组信息")
            return success_count
        except Exception as e:
            self.logger.error(f"批量插入群组信息失败: {e}")
            return 0
    
    def get_groups_by_date(self, date_str: str) -> List[dict]:
        """
        根据日期查询群组信息
        
        Args:
            date_str: 日期字符串 (YYYY-MM-DD)
            
        Returns:
            List[dict]: 群组信息列表
        """
        try:
            if not self.collection:
                self.logger.error("数据库连接未建立")
                return []
            
            # 构建日期范围查询
            from datetime import datetime
            start_date = datetime.strptime(date_str, "%Y-%m-%d")
            end_date = start_date.replace(hour=23, minute=59, second=59)
            
            query = {
                "recorded_time": {
                    "$gte": start_date,
                    "$lte": end_date
                }
            }
            
            results = list(self.collection.find(query).sort("rank", 1))
            self.logger.info(f"查询到 {len(results)} 个群组信息 (日期: {date_str})")
            return results
        except Exception as e:
            self.logger.error(f"查询群组信息失败: {e}")
            return []
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()
