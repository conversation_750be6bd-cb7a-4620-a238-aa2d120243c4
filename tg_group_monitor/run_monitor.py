#!/usr/bin/env python3
"""
运行Telegram群组监控的简单脚本
"""

import sys
import logging
from . import monitor_telegram_groups

def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("=== Telegram群组监控工具 ===")
    
    # 获取URL
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        url = input("请输入要监控的网页URL: ").strip()
    
    if not url:
        print("❌ URL不能为空")
        return
    
    print(f"🔍 开始监控: {url}")
    
    # 执行监控
    try:
        success = monitor_telegram_groups(url)
        
        if success:
            print("✅ 监控完成！数据已保存到MongoDB")
            print("📊 数据库信息:")
            print("   - 主机: localhost:27017")
            print("   - 数据库: mydatabase")
            print("   - 集合: tg_group")
        else:
            print("❌ 监控失败，请检查日志")
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
