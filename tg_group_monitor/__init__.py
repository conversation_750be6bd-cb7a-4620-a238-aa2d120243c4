"""
Telegram群组监控包

用于抓取和监控Telegram群组信息的Python包
"""

from .models import TelegramGroup
from .scraper import TelegramGroupScraper
from .database import MongoDBManager
from .main import monitor_telegram_groups
from .comparator import DataComparator
from .telegram_bot import TelegramBot

__version__ = "1.0.0"
__author__ = "Your Name"

__all__ = [
    "TelegramGroup",
    "TelegramGroupScraper",
    "MongoDBManager",
    "monitor_telegram_groups",
    "DataComparator",
    "TelegramBot"
]