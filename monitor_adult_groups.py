#!/usr/bin/env python3
"""
监控Telegram成人群组的脚本
"""

import logging
import sys
from datetime import datetime
from tg_group_monitor import monitor_telegram_groups, TelegramGroupScraper, MongoDBManager

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('tg_adult_monitor.log', encoding='utf-8')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("=== Telegram成人群组监控工具 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 目标URL
    url = "https://cn.tgstat.com/adult"
    
    print(f"🔍 目标URL: {url}")
    print("🚀 开始监控...")
    
    try:
        # 执行监控
        success = monitor_telegram_groups(url)
        
        if success:
            print("✅ 监控完成！数据已保存到MongoDB")
            
            # 显示统计信息
            with MongoDBManager() as db:
                if db.collection:
                    # 查询今天的记录
                    today = datetime.now().strftime('%Y-%m-%d')
                    today_groups = db.get_groups_by_date(today)
                    
                    print(f"📊 今日记录统计:")
                    print(f"   - 总群组数: {len(today_groups)}")
                    
                    if today_groups:
                        # 计算订阅人数统计
                        subscribers = [g['subscribers'] for g in today_groups]
                        total_subscribers = sum(subscribers)
                        avg_subscribers = total_subscribers / len(subscribers)
                        max_subscribers = max(subscribers)
                        min_subscribers = min(subscribers)
                        
                        print(f"   - 总订阅人数: {total_subscribers:,}")
                        print(f"   - 平均订阅人数: {avg_subscribers:,.0f}")
                        print(f"   - 最高订阅人数: {max_subscribers:,}")
                        print(f"   - 最低订阅人数: {min_subscribers:,}")
                        
                        # 显示前10名群组
                        print(f"\n🏆 前10名群组:")
                        for i, group in enumerate(today_groups[:10], 1):
                            print(f"   {i:2d}. {group['name'][:30]:<30} @{group['telegram_id']:<15} {group['subscribers']:>8,} 订阅者")
            
            print(f"\n📄 详细日志已保存到: tg_adult_monitor.log")
            print(f"🗄️ 数据库信息:")
            print(f"   - 主机: localhost:27017")
            print(f"   - 数据库: mydatabase")
            print(f"   - 集合: tg_group")
            
        else:
            print("❌ 监控失败，请检查日志文件")
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
        logger.info("用户中断监控操作")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        logger.error(f"监控过程中发生错误: {e}")
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
