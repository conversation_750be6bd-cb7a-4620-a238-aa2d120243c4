# 项目结构说明

## 📁 目录结构

```
pythonProject1/
├── tg_group_monitor/             # 主包目录
│   ├── __init__.py               # 包初始化文件
│   ├── models.py                 # 数据模型定义
│   ├── scraper.py                # 网页抓取模块
│   ├── database.py               # MongoDB数据库操作
│   ├── main.py                   # 主程序入口
│   ├── final_example.py          # 完整示例脚本（推荐使用）
│   ├── monitor_adult_groups.py   # 成人群组专用监控脚本
│   ├── run_monitor.py            # 通用监控脚本
│   └── query_data.py             # 数据库查询工具
│
├── tests/                        # 测试文件目录
│   ├── __init__.py               # 测试包初始化
│   ├── test_parser.py            # HTML解析功能测试
│   ├── test_real_request.py      # 真实网页请求测试
│   ├── test_full_process.py      # 完整抓取流程测试
│   └── test_database_fix.py      # 数据库修复测试
│
├── monitor.py                    # 主监控启动脚本
├── query.py                      # 数据库查询启动脚本
├── run_tests.py                  # 测试运行脚本
│
├── requirements.txt              # Python依赖包列表
├── README.md                     # 项目使用说明
├── PROJECT_SUMMARY.md            # 项目总结文档
└── STRUCTURE.md                  # 本文件（项目结构说明）
```

## 🚀 快速使用指南

### 1. 主要功能
```bash
python monitor.py      # 运行完整监控（推荐）
python query.py        # 查询数据库数据
```

### 2. 测试功能
```bash
python run_tests.py all        # 运行所有测试
python run_tests.py parser     # 运行HTML解析测试
```

### 3. 直接调用包内模块
```bash
python -m tg_group_monitor.final_example
python -m tg_group_monitor.query_data stats
```

## 📋 文件说明

### 核心模块 (tg_group_monitor/)

- **`models.py`**: 定义TelegramGroup数据模型
- **`scraper.py`**: 网页抓取和HTML解析逻辑
- **`database.py`**: MongoDB数据库操作封装
- **`main.py`**: 主程序入口，提供monitor_telegram_groups函数

### 应用脚本 (tg_group_monitor/)

- **`final_example.py`**: 完整的交互式监控脚本，包含数据展示和用户确认
- **`monitor_adult_groups.py`**: 专门用于监控成人群组的脚本
- **`query_data.py`**: 数据库查询和统计工具
- **`run_monitor.py`**: 通用的监控脚本

### 测试模块 (tests/)

- **`test_parser.py`**: 测试HTML解析功能
- **`test_real_request.py`**: 测试真实网页请求
- **`test_full_process.py`**: 测试完整的抓取流程
- **`test_database_fix.py`**: 测试数据库连接和操作

### 启动脚本 (根目录)

- **`monitor.py`**: 主监控启动脚本，调用final_example.py
- **`query.py`**: 查询启动脚本，调用query_data.py
- **`run_tests.py`**: 测试管理脚本，可运行单个或所有测试

## 🔧 开发说明

### 添加新功能
1. 在`tg_group_monitor/`目录下添加新模块
2. 在`__init__.py`中导出新功能
3. 在`tests/`目录下添加对应测试

### 运行开发测试
```bash
# 测试HTML解析
python tests/test_parser.py

# 测试真实请求
python tests/test_real_request.py

# 测试完整流程
python tests/test_full_process.py
```

### 包导入方式
```python
# 从包外部导入
from tg_group_monitor import TelegramGroupScraper, MongoDBManager

# 从包内部导入（在tg_group_monitor/内的文件中）
from .scraper import TelegramGroupScraper
from .database import MongoDBManager
```

## 📊 数据流程

1. **抓取**: `scraper.py` 从网页获取HTML并解析群组信息
2. **存储**: `database.py` 将数据保存到MongoDB
3. **查询**: `query_data.py` 从数据库检索和分析数据
4. **展示**: 各种脚本提供不同的数据展示方式

## 🎯 使用建议

- **日常监控**: 使用 `python monitor.py`
- **数据查询**: 使用 `python query.py`
- **功能测试**: 使用 `python run_tests.py all`
- **开发调试**: 直接运行 `tests/` 目录下的测试文件
