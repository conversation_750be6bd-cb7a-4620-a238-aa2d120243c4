#!/usr/bin/env python3
"""
测试完整的抓取和解析流程（不保存到数据库）
"""

import logging
from datetime import datetime
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tg_group_monitor.scraper import TelegramGroupScraper

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_full_process():
    """测试完整流程"""
    print("=== 测试完整抓取流程 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 目标URL
    url = "https://cn.tgstat.com/adult"
    
    # 创建抓取器
    scraper = TelegramGroupScraper()
    
    print(f"🔍 正在抓取: {url}")
    
    # 抓取群组信息
    groups = scraper.scrape_groups(url)
    
    if groups:
        print(f"✅ 成功抓取到 {len(groups)} 个群组")
        
        # 统计信息
        subscribers = [g.subscribers for g in groups]
        total_subscribers = sum(subscribers)
        avg_subscribers = total_subscribers / len(subscribers)
        max_subscribers = max(subscribers)
        min_subscribers = min(subscribers)
        
        print(f"\n📊 统计信息:")
        print(f"   总群组数: {len(groups)}")
        print(f"   总订阅人数: {total_subscribers:,}")
        print(f"   平均订阅人数: {avg_subscribers:,.0f}")
        print(f"   最高订阅人数: {max_subscribers:,}")
        print(f"   最低订阅人数: {min_subscribers:,}")
        
        # 显示前10名群组
        print(f"\n🏆 前10名群组:")
        for i, group in enumerate(groups[:10], 1):
            print(f"   {i:2d}. {group.name[:40]:<40} @{group.telegram_id:<15} {group.subscribers:>8,} 订阅者")
        
        # 显示后10名群组
        print(f"\n📉 后10名群组:")
        for i, group in enumerate(groups[-10:], len(groups)-9):
            print(f"   {i:2d}. {group.name[:40]:<40} @{group.telegram_id:<15} {group.subscribers:>8,} 订阅者")
        
        # 验证数据质量
        print(f"\n🔍 数据质量检查:")
        
        # 检查是否有重复的telegram_id
        telegram_ids = [g.telegram_id for g in groups]
        unique_ids = set(telegram_ids)
        if len(telegram_ids) == len(unique_ids):
            print("   ✅ 没有重复的Telegram ID")
        else:
            print(f"   ⚠️ 发现 {len(telegram_ids) - len(unique_ids)} 个重复的Telegram ID")
        
        # 检查是否有空的字段
        empty_names = sum(1 for g in groups if not g.name.strip())
        empty_ids = sum(1 for g in groups if not g.telegram_id.strip())
        zero_subscribers = sum(1 for g in groups if g.subscribers == 0)
        
        print(f"   空名称: {empty_names} 个")
        print(f"   空ID: {empty_ids} 个")
        print(f"   零订阅者: {zero_subscribers} 个")
        
        # 检查排名是否连续
        ranks = [g.rank for g in groups]
        expected_ranks = list(range(1, len(groups) + 1))
        if ranks == expected_ranks:
            print("   ✅ 排名连续正确")
        else:
            print("   ⚠️ 排名不连续")
        
        print(f"\n✅ 测试完成")
        
    else:
        print("❌ 没有抓取到任何群组")
    
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    test_full_process()
