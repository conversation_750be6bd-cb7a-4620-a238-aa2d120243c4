#!/usr/bin/env python3
"""
测试Telegram Bot功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from datetime import datetime
from tg_group_monitor.telegram_bot import TelegramBot
from tg_group_monitor.models import TelegramGroup

# 设置日志
logging.basicConfig(level=logging.INFO)

# Bot配置
BOT_TOKEN = '5184506069:AAGQWOymougL_bRU_-BauWIVy8whIlY3AI8'
CHAT_ID = '-1002898774251'

def test_bot_connection():
    """测试Bot连接"""
    print("=== 测试Telegram Bot连接 ===")
    
    bot = TelegramBot(BOT_TOKEN, CHAT_ID)
    
    print("🤖 测试Bot连接...")
    success = bot.test_connection()
    
    if success:
        print("✅ Bot连接成功")
        return True
    else:
        print("❌ Bot连接失败")
        return False

def test_send_simple_message():
    """测试发送简单消息"""
    print("\n=== 测试发送简单消息 ===")
    
    bot = TelegramBot(BOT_TOKEN, CHAT_ID)
    
    message = f"""<b>🧪 Telegram Bot测试</b>

⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 Bot状态: 正常运行
📊 功能测试: 消息发送

这是一条测试消息，用于验证Bot功能是否正常。"""
    
    print("📤 发送测试消息...")
    success = bot.send_message(message)
    
    if success:
        print("✅ 测试消息发送成功")
        return True
    else:
        print("❌ 测试消息发送失败")
        return False

def test_send_groups_message():
    """测试发送群组列表消息"""
    print("\n=== 测试发送群组列表消息 ===")
    
    bot = TelegramBot(BOT_TOKEN, CHAT_ID)
    
    # 创建测试群组数据
    test_groups = [
        TelegramGroup("测试群组1", "test1", 10000, 1),
        TelegramGroup("测试群组2", "test2", 8000, 2),
        TelegramGroup("测试群组3", "test3", 6000, 3),
        TelegramGroup("测试群组4", "test4", 4000, 4),
        TelegramGroup("测试群组5", "test5", 2000, 5),
    ]
    
    title = f"🧪 测试群组列表\n⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    message = bot.format_groups_message(test_groups, title)
    
    print("📤 发送群组列表消息...")
    success = bot.send_message(message)
    
    if success:
        print("✅ 群组列表消息发送成功")
        return True
    else:
        print("❌ 群组列表消息发送失败")
        return False

def test_send_comparison_message():
    """测试发送对比消息"""
    print("\n=== 测试发送对比消息 ===")
    
    bot = TelegramBot(BOT_TOKEN, CHAT_ID)
    
    # 创建测试数据
    previous_time = datetime(2025, 7, 13, 10, 0, 0)
    current_time = datetime(2025, 7, 13, 14, 0, 0)
    
    new_groups = [
        TelegramGroup("新增群组1", "new1", 5000, 101),
        TelegramGroup("新增群组2", "new2", 3000, 102),
    ]
    
    message = bot.format_comparison_message(
        previous_time, current_time, new_groups, 102, 100
    )
    
    print("📤 发送对比消息...")
    success = bot.send_message(message)
    
    if success:
        print("✅ 对比消息发送成功")
        return True
    else:
        print("❌ 对比消息发送失败")
        return False

def main():
    """主函数"""
    print("=== Telegram Bot功能测试 ===\n")
    
    tests = [
        ("Bot连接", test_bot_connection),
        ("简单消息", test_send_simple_message),
        ("群组列表", test_send_groups_message),
        ("对比消息", test_send_comparison_message),
    ]
    
    success_count = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                success_count += 1
            print()
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}\n")
    
    print(f"📊 测试结果: {success_count}/{len(tests)} 个测试通过")
    
    if success_count == len(tests):
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
