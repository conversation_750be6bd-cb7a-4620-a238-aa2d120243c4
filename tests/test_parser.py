#!/usr/bin/env python3
"""
测试HTML解析功能
"""

import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tg_group_monitor.scraper import TelegramGroupScraper

# 设置日志
logging.basicConfig(level=logging.DEBUG)

# 测试HTML内容（基于你提供的示例）
test_html = """
<!DOCTYPE html>
<html>
<head><title>Test</title></head>
<body>
<form id="category-list-form">
    <div>其他内容</div>
    <div class="container">
        <div class="col-12 col-sm-6 col-md-4">
            <div class="card card-body peer-item-box py-2 mb-2 mb-sm-3 border border-info-hover position-relative">
                <a class="js-btn-favorite favorite-btn favorite-btn-top popup_ajax " href="#" data-src="/my/favorites/@dyf18/create" data-id="11705415">
                    <i class="fav-icon" title="" data-toggle="tooltip" data-placement="top" data-original-title="Add to favorites"></i>
                </a>
                <a href="https://cn.tgstat.com/channel/@dyf18" class="text-body">
                    <div class="row">
                        <div class="col">
                            <div>
                                <div class="font-16 text-dark text-truncate">吃瓜ღ猎奇</div>
                                <div class="font-14 text-muted line-clamp-2 mt-1" style="min-height: 42px;"></div>
                            </div>
                            <div class="mt-2">
                                <div class="font-12 text-truncate">
                                    <b>559 838</b> subscribers
                                </div>
                            </div>
                        </div>
                        <div class="col-auto mr-n2 d-flex justify-content-end">
                            <div class="d-flex align-items-start flex-column">
                                <div class="mb-auto">
                                    <img src="//static7.tgstat.ru/channels/_100/5d/5d98749d593a52e01d0eae09c9e303e0.jpg" class="img-thumbnail rounded-circle inline-block" style="width:70px; height:70px;">
                                </div>
                                <div class="w-100">
                                    <div class="text-center text-muted font-12" title="" data-toggle="tooltip" data-html="true" data-placement="top" data-original-title="Last post in the channel: &lt;br&gt;17 hours ago" data-trigger="click">
                                        17 hours
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
        
        <div class="col-12 col-sm-6 col-md-4">
            <div class="card card-body peer-item-box py-2 mb-2 mb-sm-3 border border-info-hover position-relative">
                <a href="https://cn.tgstat.com/channel/@testgroup" class="text-body">
                    <div class="row">
                        <div class="col">
                            <div>
                                <div class="font-16 text-dark text-truncate">测试群组</div>
                                <div class="font-14 text-muted line-clamp-2 mt-1" style="min-height: 42px;"></div>
                            </div>
                            <div class="mt-2">
                                <div class="font-12 text-truncate">
                                    <b>123 456</b> subscribers
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</form>
</body>
</html>
"""

def test_parser():
    """测试解析器"""
    print("=== 测试HTML解析功能 ===")
    
    scraper = TelegramGroupScraper()
    groups = scraper.parse_groups_from_html(test_html)
    
    print(f"解析结果: 找到 {len(groups)} 个群组")
    
    for i, group in enumerate(groups, 1):
        print(f"\n群组 {i}:")
        print(f"  名称: {group.name}")
        print(f"  ID: {group.telegram_id}")
        print(f"  订阅人数: {group.subscribers:,}")
        print(f"  排名: {group.rank}")
        print(f"  记录时间: {group.recorded_time}")
    
    # 测试数据提取功能
    print("\n=== 测试数据提取功能 ===")
    
    # 测试订阅人数解析
    test_cases = ["559 838", "123 456", "1 000 000", "500"]
    for case in test_cases:
        result = scraper.parse_subscriber_count(case)
        print(f"'{case}' -> {result:,}")
    
    # 测试ID提取
    test_urls = [
        "https://cn.tgstat.com/channel/@dyf18",
        "https://cn.tgstat.com/channel/@testgroup",
        "/channel/@example"
    ]
    for url in test_urls:
        result = scraper.extract_telegram_id(url)
        print(f"'{url}' -> '{result}'")

if __name__ == "__main__":
    test_parser()
