#!/usr/bin/env python3
"""
测试真实网页请求
"""

import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tg_group_monitor.scraper import TelegramGroupScraper

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_real_request():
    """测试真实网页请求"""
    print("=== 测试真实网页请求 ===")
    
    # 目标URL
    url = "https://cn.tgstat.com/adult"
    
    # 创建抓取器
    scraper = TelegramGroupScraper()
    
    print(f"🔍 正在请求: {url}")
    
    # 获取网页内容
    html = scraper.fetch_page(url)
    
    if html:
        print(f"✅ 成功获取网页内容，长度: {len(html)} 字符")
        
        # 保存HTML到文件以便检查
        with open('downloaded_page.html', 'w', encoding='utf-8') as f:
            f.write(html)
        print("📄 HTML内容已保存到 downloaded_page.html")
        
        # 尝试解析群组信息
        print("\n🔍 正在解析群组信息...")
        groups = scraper.parse_groups_from_html(html)
        
        if groups:
            print(f"✅ 成功解析到 {len(groups)} 个群组")
            
            # 显示前5个群组
            for i, group in enumerate(groups[:5], 1):
                print(f"\n群组 {i}:")
                print(f"  名称: {group.name}")
                print(f"  ID: @{group.telegram_id}")
                print(f"  订阅人数: {group.subscribers:,}")
                print(f"  排名: #{group.rank}")
        else:
            print("❌ 没有解析到群组信息")
            
            # 检查HTML中是否包含预期的结构
            if 'category-list-form' in html:
                print("✅ 找到 category-list-form 容器")
            else:
                print("❌ 未找到 category-list-form 容器")
                
            if 'col-12 col-sm-6 col-md-4' in html:
                print("✅ 找到群组项目结构")
            else:
                print("❌ 未找到群组项目结构")
                
    else:
        print("❌ 获取网页内容失败")

def test_headers_and_cookies():
    """测试headers和cookies设置"""
    print("\n=== 测试Headers和Cookies设置 ===")
    
    scraper = TelegramGroupScraper()
    
    print("📋 当前Headers:")
    for key, value in scraper.session.headers.items():
        print(f"  {key}: {value}")
    
    print("\n🍪 当前Cookies:")
    for key, value in scraper.session.cookies.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    test_headers_and_cookies()
    test_real_request()
