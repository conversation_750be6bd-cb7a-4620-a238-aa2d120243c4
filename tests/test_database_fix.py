#!/usr/bin/env python3
"""
测试数据库修复
"""

import logging
from datetime import datetime
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tg_group_monitor.models import TelegramGroup
from tg_group_monitor.database import MongoDBManager

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_database_operations():
    """测试数据库操作"""
    print("=== 测试数据库操作修复 ===")
    
    # 创建测试数据
    test_groups = [
        TelegramGroup("测试群组1", "test1", 1000, 1),
        TelegramGroup("测试群组2", "test2", 2000, 2),
        TelegramGroup("测试群组3", "test3", 3000, 3)
    ]
    
    try:
        print("🔄 测试数据库连接和插入...")
        
        with MongoDBManager() as db:
            print(f"✅ 数据库连接成功")
            print(f"   Collection对象: {type(db.collection)}")
            print(f"   Collection是否为None: {db.collection is None}")
            
            # 测试批量插入
            success_count = db.insert_groups(test_groups)
            
            if success_count == len(test_groups):
                print(f"✅ 成功插入 {success_count} 个测试群组")
                
                # 测试查询
                today = datetime.now().strftime('%Y-%m-%d')
                today_groups = db.get_groups_by_date(today)
                
                print(f"✅ 成功查询到今日 {len(today_groups)} 个群组")
                
                # 显示测试数据
                test_data = [g for g in today_groups if g['telegram_id'].startswith('test')]
                print(f"📊 测试数据:")
                for group in test_data:
                    print(f"   - {group['name']} (@{group['telegram_id']}) - {group['subscribers']} 订阅者")
                
            else:
                print(f"⚠️ 只插入了 {success_count}/{len(test_groups)} 个群组")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_collection_check():
    """测试Collection对象检查"""
    print("\n=== 测试Collection对象检查 ===")
    
    try:
        db = MongoDBManager()
        
        print(f"连接前 - Collection: {db.collection}")
        print(f"连接前 - Collection is None: {db.collection is None}")
        
        success = db.connect()
        
        print(f"连接后 - 连接成功: {success}")
        print(f"连接后 - Collection: {type(db.collection)}")
        print(f"连接后 - Collection is None: {db.collection is None}")
        
        # 测试布尔值检查（这应该会失败）
        try:
            result = bool(db.collection)
            print(f"bool(collection): {result}")
        except Exception as e:
            print(f"❌ bool(collection) 失败（预期）: {e}")
        
        # 测试None检查（这应该成功）
        try:
            result = db.collection is None
            print(f"✅ collection is None: {result}")
        except Exception as e:
            print(f"❌ collection is None 失败: {e}")
        
        db.disconnect()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_collection_check()
    test_database_operations()
