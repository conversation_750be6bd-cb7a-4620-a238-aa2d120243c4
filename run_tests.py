#!/usr/bin/env python3
"""
运行测试脚本
"""

import sys
import os
import subprocess

def run_test(test_name):
    """运行指定的测试"""
    test_path = os.path.join("tests", f"test_{test_name}.py")
    if os.path.exists(test_path):
        print(f"🔍 运行测试: {test_name}")
        print("=" * 50)
        result = subprocess.run([sys.executable, test_path], cwd=".")
        print("=" * 50)
        return result.returncode == 0
    else:
        print(f"❌ 测试文件不存在: {test_path}")
        return False

def main():
    """主函数"""
    print("=== Telegram群组监控测试套件 ===\n")
    
    available_tests = [
        ("parser", "HTML解析功能测试"),
        ("real_request", "真实网页请求测试"),
        ("full_process", "完整抓取流程测试"),
        ("database_fix", "数据库修复测试")
    ]
    
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        if test_name == "all":
            print("🚀 运行所有测试...\n")
            success_count = 0
            for test_key, test_desc in available_tests:
                if run_test(test_key):
                    success_count += 1
                print()
            
            print(f"📊 测试结果: {success_count}/{len(available_tests)} 个测试通过")
        else:
            # 查找匹配的测试
            found = False
            for test_key, test_desc in available_tests:
                if test_key == test_name:
                    run_test(test_key)
                    found = True
                    break
            
            if not found:
                print(f"❌ 未找到测试: {test_name}")
                print("可用的测试:")
                for test_key, test_desc in available_tests:
                    print(f"  {test_key}: {test_desc}")
    else:
        print("可用的测试:")
        for i, (test_key, test_desc) in enumerate(available_tests, 1):
            print(f"  {i}. {test_key}: {test_desc}")
        
        print(f"\n使用方法:")
        print(f"  python run_tests.py all           # 运行所有测试")
        print(f"  python run_tests.py parser        # 运行HTML解析测试")
        print(f"  python run_tests.py real_request  # 运行真实请求测试")
        print(f"  python run_tests.py full_process  # 运行完整流程测试")
        print(f"  python run_tests.py database_fix  # 运行数据库测试")

if __name__ == "__main__":
    main()
