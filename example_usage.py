#!/usr/bin/env python3
"""
Telegram群组监控使用示例
"""

import logging
from tg_group_monitor import monitor_telegram_groups, TelegramGroupScraper, MongoDBManager

def main():
    """主函数示例"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 示例URL - 请替换为实际的URL
    url = "https://cn.tgstat.com/channels/top"  # 请替换为实际的URL
    
    print("=== Telegram群组监控示例 ===")
    print(f"目标URL: {url}")
    
    # 方法1: 使用便捷函数
    print("\n方法1: 使用便捷函数")
    success = monitor_telegram_groups(url)
    if success:
        print("✅ 监控完成")
    else:
        print("❌ 监控失败")
    
    # 方法2: 手动控制流程
    print("\n方法2: 手动控制流程")
    
    # 创建抓取器
    scraper = TelegramGroupScraper()
    
    # 抓取数据
    groups = scraper.scrape_groups(url)
    print(f"抓取到 {len(groups)} 个群组")
    
    # 显示前几个群组信息
    for i, group in enumerate(groups[:3], 1):
        print(f"{i}. {group}")
    
    # 保存到数据库
    if groups:
        with MongoDBManager() as db:
            success_count = db.insert_groups(groups)
            print(f"成功保存 {success_count} 个群组到数据库")
    
    print("\n=== 示例完成 ===")


if __name__ == "__main__":
    main()
